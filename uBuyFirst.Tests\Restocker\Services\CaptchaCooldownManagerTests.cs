using System;
using System.Threading;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst.Tests.Restocker.Services
{
    [TestClass]
    public class CaptchaCooldownManagerTests
    {
        [TestInitialize]
        public void Setup()
        {
            // Clear any existing cooldown before each test
            CaptchaCooldownManager.ClearCooldown();
        }

        [TestMethod]
        public void IsInCooldown_WhenNoCooldownStarted_ReturnsFalse()
        {
            // Act
            var result = CaptchaCooldownManager.IsInCooldown;

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void StartCooldown_SetsIsInCooldownToTrue()
        {
            // Act
            CaptchaCooldownManager.StartCooldown();

            // Assert
            Assert.IsTrue(CaptchaCooldownManager.IsInCooldown);
        }

        [TestMethod]
        public void ClearCooldown_SetsIsInCooldownToFalse()
        {
            // Arrange
            CaptchaCooldownManager.StartCooldown();
            Assert.IsTrue(CaptchaCooldownManager.IsInCooldown);

            // Act
            CaptchaCooldownManager.ClearCooldown();

            // Assert
            Assert.IsFalse(CaptchaCooldownManager.IsInCooldown);
        }

        [TestMethod]
        public void RemainingCooldownTime_WhenNoCooldown_ReturnsNull()
        {
            // Act
            var result = CaptchaCooldownManager.RemainingCooldownTime;

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public void RemainingCooldownTime_WhenCooldownActive_ReturnsTimeSpan()
        {
            // Arrange
            CaptchaCooldownManager.StartCooldown();

            // Act
            var result = CaptchaCooldownManager.RemainingCooldownTime;

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Value.TotalMinutes > 4.5); // Should be close to 5 minutes
            Assert.IsTrue(result.Value.TotalMinutes <= 5);
        }

        [TestMethod]
        public void IsCaptchaError_WithSessionIdError_ReturnsTrue()
        {
            // Arrange
            var errorMessage = "Session ID not found in session page HTML.";

            // Act
            var result = CaptchaCooldownManager.IsCaptchaError(errorMessage);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void IsCaptchaError_WithSessionIdErrorDifferentCase_ReturnsTrue()
        {
            // Arrange
            var errorMessage = "session id not found in session page html.";

            // Act
            var result = CaptchaCooldownManager.IsCaptchaError(errorMessage);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void IsCaptchaError_WithOtherError_ReturnsFalse()
        {
            // Arrange
            var errorMessage = "Some other error message";

            // Act
            var result = CaptchaCooldownManager.IsCaptchaError(errorMessage);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void IsCaptchaError_WithNullOrEmpty_ReturnsFalse()
        {
            // Act & Assert
            Assert.IsFalse(CaptchaCooldownManager.IsCaptchaError(null));
            Assert.IsFalse(CaptchaCooldownManager.IsCaptchaError(""));
            Assert.IsFalse(CaptchaCooldownManager.IsCaptchaError("   "));
        }

        [TestMethod]
        public void GetStatusMessage_WhenNoCooldown_ReturnsActiveMessage()
        {
            // Act
            var result = CaptchaCooldownManager.GetStatusMessage();

            // Assert
            Assert.AreEqual("Restock auto purchasing is active", result);
        }

        [TestMethod]
        public void GetStatusMessage_WhenInCooldown_ReturnsCooldownMessage()
        {
            // Arrange
            CaptchaCooldownManager.StartCooldown();

            // Act
            var result = CaptchaCooldownManager.GetStatusMessage();

            // Assert
            Assert.IsTrue(result.Contains("Restock auto purchasing paused due to captcha"));
            Assert.IsTrue(result.Contains("Resuming in"));
        }

        [TestMethod]
        public void IsInCooldown_AfterCooldownExpires_ReturnsFalse()
        {
            // This test would require waiting 5 minutes or mocking time
            // For now, we'll test the logic by manually clearing after a short wait
            
            // Arrange
            CaptchaCooldownManager.StartCooldown();
            Assert.IsTrue(CaptchaCooldownManager.IsInCooldown);

            // Simulate cooldown expiration by clearing it
            Thread.Sleep(100); // Small delay to ensure time passes
            CaptchaCooldownManager.ClearCooldown();

            // Act
            var result = CaptchaCooldownManager.IsInCooldown;

            // Assert
            Assert.IsFalse(result);
        }
    }
}
