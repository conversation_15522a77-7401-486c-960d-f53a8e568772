﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="Keywords" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Keywords.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="FormNewKeyword_btnSave_Click_test" xml:space="preserve">
    <value>test</value>
  </data>
  <data name="ColorPalette" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\ColorPalette.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Paypal" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Paypal.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="MakeOffer" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\MakeOffer.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Workspaces" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Workspaces.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Eye" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Eye.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Alert" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Alert.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Cart16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Cart16.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Settings" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Settings.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Font" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Font.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Collapse" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Collapse.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Start" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Start.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="MakeOfferDark" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\MakeOfferDark.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Stop" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Stop.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="telegram" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\telegram.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Help" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Help.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Buy1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Buy.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="binbloop" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\binbloop.wav;System.IO.MemoryStream, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="uGrad" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\uGrad.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Edit" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Edit.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="SquareEmpty" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\SquareEmpty.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Expand" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Expand.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Views" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Views.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Highlight" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Highlight.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Up" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Up.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Sync" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Sync.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Export" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Export.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Reload" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Reload.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Shortcut" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Shortcut.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Add" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Add.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Checkout" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Checkout.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Import" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Import.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="ebay" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\ebay.jpg;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="OpenFile" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\OpenFile.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Filter" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Filter.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Support" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Support.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="CustomColumns" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\CustomColumns.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Down" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Down.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="CreateSubsearch" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\CreateSubsearch.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Accounts" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Accounts.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Subscription" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Subscription.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="SortAscending" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\SortAscending.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Terms" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Terms.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Clear" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Clear.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Dock" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Dock.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Panels" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Panels.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="ResetLayout" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\ResetLayout.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Remove" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Remove.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="NormalSize" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\NormalSize.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Pushbullet" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Pushbullet.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="CreditCard" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\CreditCard.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="CheckMark" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\CheckMark.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Ignore-Seller" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Ignore-Seller.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Blocked" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Blocked.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="amazon" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\amazon.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="SearchSoldOnEbay" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\SearchSoldOnEbay.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="AddToWatchlist" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\AddToWatchlist.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Ignore-Seller1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Ignore-Seller1.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="FolderOpen" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\FolderOpen.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Folder" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Folder.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Rename" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Rename.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="folder-remove" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\folder-remove.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="search-add" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\search-add.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="search-check-multiple" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\search-check-multiple.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="search-uncheck" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\search-uncheck.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="NewFolder" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\NewFolder.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="duplicate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\duplicate.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="checkbox-check" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\checkbox-check.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="add-task-list" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\add-task-list.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="search" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\search.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
</root>