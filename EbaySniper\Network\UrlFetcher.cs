using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using uBuyFirst.Network;
using uBuyFirst.Tools;

public class UrlFetcher
{
    private readonly HttpClient _httpClient;
    private readonly int _retryCount;
    private string _chromeVersion;
    private readonly CookieContainer _cookieContainer;

    public UrlFetcher(string chromeVersion, CookieContainer cookieContainer, int retryCount = 4)
    {
        _chromeVersion = chromeVersion ?? throw new ArgumentNullException(nameof(chromeVersion));
        _cookieContainer = cookieContainer ?? throw new ArgumentNullException(nameof(cookieContainer));
        _retryCount = retryCount;

        var handler = new Http2CustomHandler(Helpers.BuyItNowConfirmation)
        {
            CookieContainer = _cookieContainer,
            AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate,
            CookieUsePolicy = CookieUsePolicy.UseSpecifiedCookieContainer,
            WindowsProxyUsePolicy = WindowsProxyUsePolicy.UseWinInetProxy
        };
        _httpClient = new HttpClient(handler);
        ConfigureHttpClient(_httpClient);
    }

    public async Task<string> FetchUrlUsingCookies(string url, HttpMethod method = null, Dictionary<string, string> additionalHeaders = null, int timeoutSeconds = 10)
    {
        method ??= HttpMethod.Get;

        for (var i = 0; i < _retryCount; i++)
        {
            try
            {
                var request = new HttpRequestMessage(method, new Uri(url)) { Version = new Version(2, 0) };
                AddAdditionalHeaders(request, additionalHeaders);

                var response = await _httpClient.SendAsync(request);

                if (response?.RequestMessage?.RequestUri?.AbsoluteUri?.Contains("https://www.ebay.com/splashui/captcha") == true)
                {
                    Process.Start(new ProcessStartInfo { FileName = response.RequestMessage.RequestUri.AbsoluteUri, UseShellExecute = true });
                    return "";
                }

                if (response?.Content != null)
                {
                    return await response.Content.ReadAsStringAsync();
                }
            }
            catch (Exception ex)
            {
                HandleException(ex);
            }
        }

        return "";
    }

    private void ConfigureHttpClient(HttpClient httpClient)
    {
        ServicePointManager.Expect100Continue = false;
        ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;

        var chromeVersionShort = Helpers.RegexValue(_chromeVersion, "Chrome/([0-9]+)");
        var chromeVersionLong = Helpers.RegexValue(_chromeVersion, "Chrome/([0-9\\.]+)");
        var sec_ch_ua = $"\".Not/A)Brand\";v=\"99\", \"Google Chrome\";v=\"{chromeVersionShort}\", \"Chromium\";v=\"{chromeVersionShort}\"";

        httpClient.Timeout = TimeSpan.FromSeconds(30); // Default timeout
        httpClient.DefaultRequestHeaders.Add("Accept-Language", "en-us,en;q=0.5");
        httpClient.DefaultRequestHeaders.Add("Accept-Encoding", "gzip,deflate");
        httpClient.DefaultRequestHeaders.Add("Accept-Charset", "utf-8;q=0.7,*;q=0.7");
        httpClient.DefaultRequestHeaders.Add("User-Agent", _chromeVersion.Replace(chromeVersionLong, chromeVersionShort + ".0.0.0"));

        httpClient.DefaultRequestHeaders.Add("sec-ch-ua", sec_ch_ua);
        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-mobile", "?0");
        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-platform", "\"Windows\"");
        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-platform-version", "\"10.0.0\"");
        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-model", "\"\"");
        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-full-version", $"\"{chromeVersionLong}\"");

        httpClient.DefaultRequestHeaders.Add("upgrade-insecure-requests", "1");
        httpClient.DefaultRequestHeaders.Add("sec-fetch-site", "none");
        httpClient.DefaultRequestHeaders.Add("sec-fetch-mode", "navigate");
        httpClient.DefaultRequestHeaders.Add("sec-fetch-user", "?1");
        httpClient.DefaultRequestHeaders.Add("sec-fetch-dest", "document");

        httpClient.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.98");

    }

    private static void AddCookiesToRequest(HttpRequestMessage request, Uri uri)
    {
        var cookieContainer = new CookieContainer();
        // Add cookies to cookieContainer
        request.Headers.Add("Cookie", cookieContainer.GetCookieHeader(uri));
    }

    private void AddAdditionalHeaders(HttpRequestMessage request, Dictionary<string, string> additionalHeaders)
    {
        if (additionalHeaders == null)
            return;

        foreach (var header in additionalHeaders)
        {
            request.Headers.TryAddWithoutValidation(header.Key, header.Value);
        }
    }

    private void HandleException(Exception ex)
    {
        // Exception handling logic
        Console.WriteLine($@"Error occurred: {ex.Message}");
    }
}
