﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace uBuyFirst.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("uBuyFirst.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Accounts {
            get {
                object obj = ResourceManager.GetObject("Accounts", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Add {
            get {
                object obj = ResourceManager.GetObject("Add", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage add_task_list {
            get {
                object obj = ResourceManager.GetObject("add-task-list", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage AddToWatchlist {
            get {
                object obj = ResourceManager.GetObject("AddToWatchlist", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Alert {
            get {
                object obj = ResourceManager.GetObject("Alert", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage amazon {
            get {
                object obj = ResourceManager.GetObject("amazon", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.IO.UnmanagedMemoryStream similar to System.IO.MemoryStream.
        /// </summary>
        public static System.IO.UnmanagedMemoryStream binbloop {
            get {
                return ResourceManager.GetStream("binbloop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Blocked {
            get {
                object obj = ResourceManager.GetObject("Blocked", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Buy1 {
            get {
                object obj = ResourceManager.GetObject("Buy1", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        public static System.Drawing.Bitmap Cart16 {
            get {
                object obj = ResourceManager.GetObject("Cart16", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage checkbox_check {
            get {
                object obj = ResourceManager.GetObject("checkbox-check", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage CheckMark {
            get {
                object obj = ResourceManager.GetObject("CheckMark", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Checkout {
            get {
                object obj = ResourceManager.GetObject("Checkout", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Clear {
            get {
                object obj = ResourceManager.GetObject("Clear", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Collapse {
            get {
                object obj = ResourceManager.GetObject("Collapse", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage ColorPalette {
            get {
                object obj = ResourceManager.GetObject("ColorPalette", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage CreateSubsearch {
            get {
                object obj = ResourceManager.GetObject("CreateSubsearch", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage CreditCard {
            get {
                object obj = ResourceManager.GetObject("CreditCard", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage CustomColumns {
            get {
                object obj = ResourceManager.GetObject("CustomColumns", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Dock {
            get {
                object obj = ResourceManager.GetObject("Dock", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Down {
            get {
                object obj = ResourceManager.GetObject("Down", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage duplicate {
            get {
                object obj = ResourceManager.GetObject("duplicate", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        public static System.Drawing.Bitmap ebay {
            get {
                object obj = ResourceManager.GetObject("ebay", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Edit {
            get {
                object obj = ResourceManager.GetObject("Edit", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Expand {
            get {
                object obj = ResourceManager.GetObject("Expand", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Export {
            get {
                object obj = ResourceManager.GetObject("Export", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Eye {
            get {
                object obj = ResourceManager.GetObject("Eye", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Filter {
            get {
                object obj = ResourceManager.GetObject("Filter", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Folder {
            get {
                object obj = ResourceManager.GetObject("Folder", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage folder_remove {
            get {
                object obj = ResourceManager.GetObject("folder-remove", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage FolderOpen {
            get {
                object obj = ResourceManager.GetObject("FolderOpen", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Font {
            get {
                object obj = ResourceManager.GetObject("Font", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to test.
        /// </summary>
        public static string FormNewKeyword_btnSave_Click_test {
            get {
                return ResourceManager.GetString("FormNewKeyword_btnSave_Click_test", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Help {
            get {
                object obj = ResourceManager.GetObject("Help", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Highlight {
            get {
                object obj = ResourceManager.GetObject("Highlight", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Ignore_Seller {
            get {
                object obj = ResourceManager.GetObject("Ignore-Seller", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Ignore_Seller1 {
            get {
                object obj = ResourceManager.GetObject("Ignore-Seller1", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Import {
            get {
                object obj = ResourceManager.GetObject("Import", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Keywords {
            get {
                object obj = ResourceManager.GetObject("Keywords", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage MakeOffer {
            get {
                object obj = ResourceManager.GetObject("MakeOffer", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage MakeOfferDark {
            get {
                object obj = ResourceManager.GetObject("MakeOfferDark", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage NewFolder {
            get {
                object obj = ResourceManager.GetObject("NewFolder", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage NormalSize {
            get {
                object obj = ResourceManager.GetObject("NormalSize", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage OpenFile {
            get {
                object obj = ResourceManager.GetObject("OpenFile", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Panels {
            get {
                object obj = ResourceManager.GetObject("Panels", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Paypal {
            get {
                object obj = ResourceManager.GetObject("Paypal", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Pushbullet {
            get {
                object obj = ResourceManager.GetObject("Pushbullet", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Reload {
            get {
                object obj = ResourceManager.GetObject("Reload", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Remove {
            get {
                object obj = ResourceManager.GetObject("Remove", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Rename {
            get {
                object obj = ResourceManager.GetObject("Rename", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage ResetLayout {
            get {
                object obj = ResourceManager.GetObject("ResetLayout", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage search {
            get {
                object obj = ResourceManager.GetObject("search", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage search_add {
            get {
                object obj = ResourceManager.GetObject("search-add", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage search_check_multiple {
            get {
                object obj = ResourceManager.GetObject("search-check-multiple", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage search_uncheck {
            get {
                object obj = ResourceManager.GetObject("search-uncheck", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage SearchSoldOnEbay {
            get {
                object obj = ResourceManager.GetObject("SearchSoldOnEbay", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Settings {
            get {
                object obj = ResourceManager.GetObject("Settings", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Shortcut {
            get {
                object obj = ResourceManager.GetObject("Shortcut", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage SortAscending {
            get {
                object obj = ResourceManager.GetObject("SortAscending", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage SquareEmpty {
            get {
                object obj = ResourceManager.GetObject("SquareEmpty", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Start {
            get {
                object obj = ResourceManager.GetObject("Start", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Stop {
            get {
                object obj = ResourceManager.GetObject("Stop", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Subscription {
            get {
                object obj = ResourceManager.GetObject("Subscription", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Support {
            get {
                object obj = ResourceManager.GetObject("Support", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Sync {
            get {
                object obj = ResourceManager.GetObject("Sync", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage telegram {
            get {
                object obj = ResourceManager.GetObject("telegram", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Terms {
            get {
                object obj = ResourceManager.GetObject("Terms", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        public static System.Drawing.Bitmap uGrad {
            get {
                object obj = ResourceManager.GetObject("uGrad", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Up {
            get {
                object obj = ResourceManager.GetObject("Up", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Views {
            get {
                object obj = ResourceManager.GetObject("Views", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Workspaces {
            get {
                object obj = ResourceManager.GetObject("Workspaces", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
    }
}
