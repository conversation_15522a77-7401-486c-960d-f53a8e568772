﻿using System.Drawing;
using System.Linq;

namespace uBuyFirst.Prefs
{
    public static class Config
    {
        public const string AffiliateRedirectUrl = "https://data.ubuyfirst.net/uBuyFirst/redirect/";
        public static readonly string[] UpdateUrls = { "http://data.ubuyfirst.net/uBuyFirst/update.php", "http://ubuyfirst.com/uBuyFirst/update.php" };
        public const string PixelUrl = "https://data.ubuyfirst.net/uBuyFirst/pixel/index.php";
        public const string ExistingLicenseUrl = "https://data.ubuyfirst.net/uBuyFirst/f/getexistinglicense.php";
        public const string TrialUrl1 = "https://wsapi.ubuyfirst.net/uBuyFirst/trial/trial.php";
        public const string TrialUrl2 = "https://data.ubuyfirst.net/uBuyFirst/f/trial.php";
        public const string ZipUrl = "https://data.ubuyfirst.net/uBuyFirst/cefsharp/Chrome.zip";
        public const string AmazonSearchAffiliateUrl = "https://data.ubuyfirst.net/uBuyFirst/searchamazon.php";
        public const string CampaignID = "**********";
        public static bool SafeEnabled { get; set; } = false;
        public static double BrowseSearchCoefficient { get; set; } = 1;
        public static double FindingAPICoefficient { get; set; } = 1;
    }
}
